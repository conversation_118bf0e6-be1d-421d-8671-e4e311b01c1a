{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-clipboard-list"></i> Exams
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-clipboard-list{% endblock title-icon %}
{% block title %}Exams{% endblock title %}
{% block subtitle %}View and manage exam schedules{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Exam Schedule</h5>
        </div>
        <div class="card-body">
          {% if exams %}
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>S/N</th>
                    <th>Exam Name</th>
                    <th>Session</th>
                    <th>Term</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for exam in exams %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ exam.name }}</td>
                    <td>{{ exam.session|default:"N/A" }}</td>
                    <td>{{ exam.term|default:"N/A" }}</td>
                    <td>{{ exam.start_date|date:"d M Y"|default:"N/A" }}</td>
                    <td>{{ exam.end_date|date:"d M Y"|default:"N/A" }}</td>
                    <td>
                      {% if exam.status == 'pending' %}
                        <span class="badge bg-warning">Pending</span>
                      {% elif exam.status == 'ongoing' %}
                        <span class="badge bg-primary">Ongoing</span>
                      {% elif exam.status == 'completed' %}
                        <span class="badge bg-success">Completed</span>
                      {% else %}
                        <span class="badge bg-secondary">{{ exam.status|title }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <a href="{% url 'teacher_marks_entry' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-edit"></i> Enter Marks
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Exams</h5>
              <p class="text-muted">No exams are currently scheduled.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
