// Responsive Sidebar fix script
$(document).ready(function() {
    // Force AdminLTE to use our custom styles
    $.fn.overlayScrollbars = function() { return this; };

    // Mobile breakpoints
    const MOBILE_BREAKPOINT = 768;
    const TABLET_BREAKPOINT = 992;

    // Add colored icons to sidebar
    function addColoredIcons() {
        // Define colors for icons
        var iconColors = [
            '#FF5252', // red
            '#4FC3F7', // blue
            '#9C27B0', // purple
            '#FFC107', // yellow
            '#4CAF50', // green
            '#FF9800', // orange
            '#E91E63', // pink
            '#9C27B0', // purple
            '#FF5722', // deep orange
            '#2196F3'  // blue
        ];

        // Apply colors to icons
        $('.nav-sidebar .nav-item').each(function(index) {
            var color = iconColors[index % iconColors.length];
            $(this).find('.nav-link .nav-icon').css('color', color);
        });
    }

    // Check if device is mobile or tablet
    function isMobile() {
        return $(window).width() < MOBILE_BREAKPOINT;
    }

    function isTablet() {
        return $(window).width() < TABLET_BREAKPOINT && $(window).width() >= MOBILE_BREAKPOINT;
    }

    function isMobileOrTablet() {
        return $(window).width() < TABLET_BREAKPOINT;
    }

    // Create mobile overlay
    function createMobileOverlay() {
        if ($('.sidebar-overlay').length === 0) {
            $('body').append('<div class="sidebar-overlay"></div>');
        }
    }

    // Show mobile overlay
    function showMobileOverlay() {
        $('.sidebar-overlay').addClass('show');
    }

    // Hide mobile overlay
    function hideMobileOverlay() {
        $('.sidebar-overlay').removeClass('show');
    }

    // Apply sidebar styles based on current state and screen size
    function applySidebarStyles() {
        const windowWidth = $(window).width();

        if (isMobileOrTablet()) {
            // Mobile and tablet styles are handled by CSS
            // Just ensure content wrapper has correct margins
            $('.content-wrapper, .main-footer, .main-header').css({
                'margin-left': '0',
                'width': '100%'
            });

            // Remove any desktop-specific classes
            $('body').removeClass('sidebar-mini sidebar-collapse');

        } else {
            // Desktop styles
            if ($('body').hasClass('sidebar-collapse')) {
                // Collapsed state
                $('.main-sidebar').css({
                    'width': '4.6rem',
                    'overflow': 'visible',
                    'transform': 'translateX(0)'
                });

                $('.content-wrapper').css({
                    'margin-left': '4.6rem',
                    'width': 'calc(100% - 4.6rem)'
                });

                $('.main-footer').css({
                    'margin-left': '4.6rem',
                    'width': 'calc(100% - 4.6rem)'
                });
            } else {
                // Expanded state
                $('.main-sidebar').css({
                    'width': '250px',
                    'overflow': 'hidden',
                    'transform': 'translateX(0)'
                });

                $('.content-wrapper').css({
                    'margin-left': '250px',
                    'width': 'calc(100% - 250px)'
                });

                $('.main-footer').css({
                    'margin-left': '250px',
                    'width': 'calc(100% - 250px)'
                });
            }

            // Hide mobile overlay on desktop
            hideMobileOverlay();
            $('body').removeClass('sidebar-open');
        }
    }

    // Handle mobile sidebar toggle
    function toggleMobileSidebar() {
        if (isMobileOrTablet()) {
            if ($('body').hasClass('sidebar-open')) {
                // Close sidebar
                $('body').removeClass('sidebar-open');
                hideMobileOverlay();
            } else {
                // Open sidebar
                $('body').addClass('sidebar-open');
                showMobileOverlay();
            }
        }
    }

    // Close mobile sidebar
    function closeMobileSidebar() {
        if (isMobileOrTablet()) {
            $('body').removeClass('sidebar-open');
            hideMobileOverlay();
        }
    }

    // Initialize responsive sidebar
    function initResponsiveSidebar() {
        // Create mobile overlay
        createMobileOverlay();

        // Add colored icons
        addColoredIcons();

        // Apply initial styles
        applySidebarStyles();

        // Handle sidebar toggle button
        $('[data-widget="pushmenu"]').off('click.responsive').on('click.responsive', function(e) {
            e.preventDefault();

            if (isMobileOrTablet()) {
                // Mobile/tablet behavior
                toggleMobileSidebar();
            } else {
                // Desktop behavior - let AdminLTE handle it
                $('body').toggleClass('sidebar-collapse');
                setTimeout(function() {
                    applySidebarStyles();
                }, 300);
            }
        });

        // Handle overlay click to close sidebar
        $(document).off('click.overlay').on('click.overlay', '.sidebar-overlay', function() {
            closeMobileSidebar();
        });

        // Handle escape key to close sidebar
        $(document).off('keydown.sidebar').on('keydown.sidebar', function(e) {
            if (e.keyCode === 27 && $('body').hasClass('sidebar-open')) {
                closeMobileSidebar();
            }
        });

        // Close sidebar when clicking on nav links on mobile
        $('.nav-sidebar .nav-link').off('click.mobile').on('click.mobile', function() {
            if (isMobileOrTablet() && !$(this).parent().hasClass('has-treeview')) {
                setTimeout(function() {
                    closeMobileSidebar();
                }, 150);
            }
        });

        // Handle window resize
        $(window).off('resize.sidebar').on('resize.sidebar', function() {
            // Debounce resize events
            clearTimeout(window.sidebarResizeTimeout);
            window.sidebarResizeTimeout = setTimeout(function() {
                applySidebarStyles();

                // Close mobile sidebar if switching to desktop
                if (!isMobileOrTablet()) {
                    closeMobileSidebar();
                }
            }, 250);
        });

        // Handle AdminLTE events
        $(document).off('collapsed.lte.pushmenu shown.lte.pushmenu').on('collapsed.lte.pushmenu shown.lte.pushmenu', function() {
            if (!isMobileOrTablet()) {
                setTimeout(function() {
                    applySidebarStyles();
                }, 300);
            }
        });

        // Prevent body scroll when sidebar is open on mobile
        $('body').on('sidebar-open', function() {
            if (isMobileOrTablet()) {
                $('body').css('overflow', 'hidden');
            }
        });

        $('body').on('sidebar-close', function() {
            $('body').css('overflow', '');
        });

        // Trigger custom events
        $(document).on('click.overlay', '.sidebar-overlay', function() {
            $('body').trigger('sidebar-close');
        });

        $('[data-widget="pushmenu"]').on('click', function() {
            if (isMobileOrTablet()) {
                if ($('body').hasClass('sidebar-open')) {
                    $('body').trigger('sidebar-close');
                } else {
                    $('body').trigger('sidebar-open');
                }
            }
        });
    }

    // Initialize everything
    initResponsiveSidebar();

    // Reinitialize on AJAX content load (if needed)
    $(document).on('DOMContentLoaded ajaxComplete', function() {
        // Only reinitialize icons, not the entire sidebar
        addColoredIcons();
    });
});
