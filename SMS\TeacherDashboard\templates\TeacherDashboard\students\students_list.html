{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-graduate"></i> My Students
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-graduate{% endblock title-icon %}
{% block title %}My Students{% endblock title %}
{% block subtitle %}View and manage your assigned students{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Students List</h5>
        </div>
        <div class="card-body">
          {% if students %}
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>Registration No.</th>
                    <th>Name</th>
                    <th>Class</th>
                    <th>Section</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for student in students %}
                  <tr>
                    <td>{{ student.registration_number|default:"N/A" }}</td>
                    <td>{{ student.fullname }}</td>
                    <td>{{ student.current_class|default:"N/A" }}</td>
                    <td>{{ student.section|default:"N/A" }}</td>
                    <td>
                      <span class="badge {% if student.current_status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ student.current_status|title }}
                      </span>
                    </td>
                    <td>
                      <a href="{% url 'teacher_student_detail' student.pk %}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i> View
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Students Found</h5>
              <p class="text-muted">No students are currently assigned to you.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
